using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO.Notification;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.InternalAPI.Controllers
{
    /// <summary>
    /// Controller for internal job operations like checking property expirations
    /// </summary>
    [Route("api/internal/[controller]")]
    [ApiController]
    public class JobsController : BaseController
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly INotificationService _notificationService;
        private readonly IUserService _userService;
        private readonly ILogger<JobsController> _logger;

        public JobsController(
            IUnitOfWork unitOfWork,
            INotificationService notificationService,
            IUserService userService,
            ILogger<JobsController> logger)
        {
            _unitOfWork = unitOfWork;
            _notificationService = notificationService;
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Check for properties that are expiring and send notifications
        /// </summary>
        /// <returns></returns>
        [HttpPost("check-expirations")]
        public async Task<IActionResult> CheckExpirations()
        {
            try
            {
                _logger.LogInformation("Starting property expiration check job at {DateTime}", DateTime.UtcNow);

                var today = DateOnly.FromDateTime(DateTime.UtcNow);
                var threeDaysFromNow = today.AddDays(3);
                var oneDayFromNow = today.AddDays(1);

                // Query to get properties expiring in 3 days, 1 day, or today
                var expiringProperties = await _unitOfWork.Properties.GetQueryable()
                    .Include(p => p.Owner)
                    .Where(p => p.Status == PropertyStatus.Approved &&
                               (DateOnly.FromDateTime(p.ExpiresAt.Date) == threeDaysFromNow ||
                                DateOnly.FromDateTime(p.ExpiresAt.Date) == oneDayFromNow ||
                                DateOnly.FromDateTime(p.ExpiresAt.Date) == today))
                    .ToListAsync();

                // Query to get properties that are already expired but still have Approved status
                var alreadyExpiredProperties = await _unitOfWork.Properties.GetQueryable()
                    .Where(p => p.Status == PropertyStatus.Approved &&
                               DateOnly.FromDateTime(p.ExpiresAt.Date) < today)
                    .ToListAsync();

                _logger.LogInformation("Found {ExpiringCount} properties expiring soon and {ExpiredCount} properties already expired",
                    expiringProperties.Count, alreadyExpiredProperties.Count);

                var processedCount = 0;
                var expiredCount = 0;

                // Process properties that are expiring soon (with notifications)
                foreach (var property in expiringProperties)
                {
                    var expiryDate = DateOnly.FromDateTime(property.ExpiresAt.Date);
                    var daysUntilExpiry = (expiryDate.ToDateTime(TimeOnly.MinValue) - today.ToDateTime(TimeOnly.MinValue)).Days;

                    try
                    {
                        if (daysUntilExpiry == 3)
                        {
                            // 3 days remaining: Send email only
                            await SendExpirationNotification(property, 3, NotificationChannel.Email);
                        }
                        else if (daysUntilExpiry == 1)
                        {
                            // 1 day remaining: Send email, push, and in-app
                            await SendExpirationNotification(property, 1, NotificationChannel.Email | NotificationChannel.Push | NotificationChannel.InApp);
                        }
                        else if (daysUntilExpiry == 0)
                        {
                            // Expired today: Send all notifications and update status
                            await SendExpirationNotification(property, 0, NotificationChannel.Email | NotificationChannel.Push | NotificationChannel.InApp);

                            // Update property status to Expired
                            property.Status = PropertyStatus.Expired;
                            expiredCount++;
                        }

                        processedCount++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing property {PropertyId} for expiration notification", property.Id);
                    }
                }

                // Process properties that are already expired (no notifications, just update status)
                foreach (var property in alreadyExpiredProperties)
                {
                    try
                    {
                        property.Status = PropertyStatus.Expired;
                        expiredCount++;
                        processedCount++;

                        _logger.LogInformation("Updated expired property {PropertyId} status to Expired (expired on {ExpiryDate})",
                            property.Id, property.ExpiresAt.ToString("dd/MM/yyyy"));
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error updating expired property {PropertyId} status", property.Id);
                    }
                }

                // Save all changes at once
                if (expiredCount > 0)
                {
                    await _unitOfWork.SaveChangesAsync();
                }

                _logger.LogInformation("Property expiration check completed. Processed: {ProcessedCount}, Expired: {ExpiredCount}", 
                    processedCount, expiredCount);

                return Ok(new
                {
                    Success = true,
                    Message = "Property expiration check completed successfully",
                    ProcessedCount = processedCount,
                    ExpiredCount = expiredCount,
                    Timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during property expiration check job");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "An error occurred during property expiration check",
                    Error = ex.Message,
                    Timestamp = DateTime.UtcNow
                });
            }
        }

        private async Task SendExpirationNotification(Domain.Entities.Property property, int daysRemaining, NotificationChannel channels)
        {
            var owner = property.Owner;
            if (owner == null)
            {
                _logger.LogWarning("Property {PropertyId} has no owner, skipping notification", property.Id);
                return;
            }

            string title, message, emailType;
            NotificationType notificationType;

            if (daysRemaining == 3)
            {
                title = "Bài đăng sắp hết hạn trong 3 ngày";
                message = $"Bài đăng '{property.Name}' của bạn sẽ hết hạn vào ngày {property.ExpiresAt:dd/MM/yyyy}. Hãy gia hạn để tiếp tục hiển thị.";
                emailType = "PropertyExpiring3Days";
                notificationType = NotificationType.ListingExpired;
            }
            else if (daysRemaining == 1)
            {
                title = "Bài đăng sắp hết hạn trong 1 ngày";
                message = $"Bài đăng '{property.Name}' của bạn sẽ hết hạn vào ngày mai ({property.ExpiresAt:dd/MM/yyyy}). Hãy gia hạn ngay để tránh bị ẩn.";
                emailType = "PropertyExpiring1Day";
                notificationType = NotificationType.ListingExpired;
            }
            else
            {
                title = "Bài đăng đã hết hạn";
                message = $"Bài đăng '{property.Name}' của bạn đã hết hạn và đã được ẩn khỏi danh sách. Hãy gia hạn để hiển thị lại.";
                emailType = "PropertyExpired";
                notificationType = NotificationType.ListingExpired;
            }

            var notificationRequest = new NotificationRequest
            {
                TargetChannels = channels,
                RecipientId = owner.Id,
                RecipientEmail = owner.Email,
                RelatedPropertyId = property.Id,
                InAppNotificationType = notificationType,
                EmailType = emailType,
                Title = title,
                Message = message,
                Data = new Dictionary<string, string>
                {
                    { "user_name", owner.FullName },
                    { "listing_title", property.Name },
                    { "propertyCode", property.Code.ToString() },
                    { "propertyId", property.Id.ToString() },
                    { "expiry_date", property.ExpiresAt.ToString("dd/MM/yyyy") },
                    { "days_remaining", daysRemaining.ToString() },
                    { "actionUrl", $"/properties/{property.Id}" }
                }
            };

            await _notificationService.SendAsync(notificationRequest);
            _logger.LogInformation("Sent expiration notification for property {PropertyId} to user {UserId}, days remaining: {DaysRemaining}", 
                property.Id, owner.Id, daysRemaining);
        }
    }
}
